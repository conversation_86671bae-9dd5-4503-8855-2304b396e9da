# 时间序列异常检测系统

一个基于传统机器学习的实时异常检测系统，专门用于监控API响应时间、系统性能指标等时间序列数据。

## 📁 项目结构

```
time_series_prediction/
├── README.md                    # 项目说明（本文件）
├── 快速开始指南.md              # 5分钟快速上手指南
├── 详细使用说明文档.md          # 超详细的技术文档
├── requirements.txt             # Python依赖包
├── 
├── 核心文件/
├── generate_sample_data.py      # 生成模拟数据
├── anomaly_detector.py          # 核心异常检测类
├── time_series_predictor.py     # 多种预测算法实现
├── simulate_anomalies.py        # 异常模拟和测试
├── 
├── 生产环境文件/
├── production_detector.py       # 生产环境检测器
├── api_server.py               # HTTP API服务
├── prometheus_exporter.py      # Prometheus指标导出
├── detector_config.json        # 配置文件
├── 
├── 部署文件/
├── Dockerfile                  # Docker镜像构建
├── docker-compose.yml          # Docker Compose配置
├── k8s-deployment.yaml         # Kubernetes部署配置
├── 
└── 数据文件/
    ├── api_response_time.csv           # 生成的模拟数据
    └── api_response_time_with_anomalies.csv  # 包含异常的测试数据
```

## 🎯 核心功能

- ✅ **实时异常检测**：毫秒级响应，适合生产环境
- ✅ **多种预测算法**：移动平均、指数平滑、季节性预测等
- ✅ **智能基线学习**：自动学习正常模式，适应业务周期
- ✅ **可配置阈值**：根据业务需求调整敏感度
- ✅ **完整的API服务**：HTTP接口，易于集成
- ✅ **Prometheus集成**：支持现代监控栈
- ✅ **容器化部署**：Docker和Kubernetes支持

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install pandas numpy scikit-learn matplotlib
```

### 2. 生成测试数据
```bash
python generate_sample_data.py
```

### 3. 运行异常检测
```bash
python anomaly_detector.py
```

### 4. 测试异常检测效果
```bash
python simulate_anomalies.py
```

## 📖 文件详细说明

### 核心文件

#### `anomaly_detector.py` - 异常检测核心类
**作用**：实现异常检测的核心逻辑
**主要功能**：
- `AnomalyDetector`类：核心检测器
- `load_data()`：加载历史数据
- `build_baseline()`：建立正常模式基线
- `detect_real_time_anomaly()`：实时异常检测
- `calculate_anomaly_score()`：计算异常分数

**使用场景**：直接在Python代码中集成异常检测功能

#### `generate_sample_data.py` - 数据生成器
**作用**：生成模拟的API响应时间数据
**特点**：
- 模拟真实业务模式（工作日vs周末，白天vs夜间）
- 包含随机噪声和偶发异常
- 生成720个小时（30天）的数据

**使用场景**：测试和演示异常检测效果

#### `time_series_predictor.py` - 多算法预测器
**作用**：实现多种时间序列预测算法
**包含算法**：
- 简单移动平均
- 指数平滑
- 线性趋势预测
- 季节性朴素预测

**使用场景**：对比不同算法的效果，选择最适合的方法

#### `simulate_anomalies.py` - 异常模拟测试
**作用**：注入人工异常，测试检测效果
**模拟场景**：
- 数据库连接池耗尽（响应时间激增）
- 内存泄漏（性能逐渐下降）
- 下游服务故障（持续高延迟）

**使用场景**：验证异常检测的准确性和可靠性

### 生产环境文件

#### `production_detector.py` - 生产级检测器
**作用**：生产环境使用的增强版检测器
**增强功能**：
- 配置文件支持
- 错误处理和日志记录
- 初始化状态管理
- 安全的默认值

**使用场景**：在生产环境中稳定运行

#### `api_server.py` - HTTP API服务
**作用**：提供HTTP接口的异常检测服务
**API端点**：
- `GET /health`：健康检查
- `POST /api/v1/detect`：单次异常检测
- `POST /api/v1/batch-detect`：批量检测

**使用场景**：微服务架构，通过HTTP调用异常检测

#### `prometheus_exporter.py` - Prometheus集成
**作用**：将异常检测结果导出为Prometheus指标
**导出指标**：
- `api_anomaly_score`：异常分数
- `api_anomaly_total`：异常总数
- `anomaly_detection_duration_seconds`：检测耗时

**使用场景**：集成到Prometheus + Grafana监控栈

### 配置和部署文件

#### `detector_config.json` - 配置文件
**作用**：集中管理系统配置
**配置项**：
- 数据文件路径
- 基线天数
- 告警阈值
- 日志配置
- 告警通知配置

#### `Dockerfile` - Docker镜像
**作用**：构建容器化的异常检测服务
**特点**：
- 基于Python 3.9
- 包含所有依赖
- 暴露API和指标端口

#### `docker-compose.yml` - 完整监控栈
**作用**：一键部署包含Prometheus和Grafana的完整监控系统
**包含服务**：
- anomaly-detector：异常检测服务
- prometheus：指标收集
- grafana：可视化面板

#### `k8s-deployment.yaml` - Kubernetes部署
**作用**：在Kubernetes集群中部署异常检测服务
**特点**：
- 高可用部署（2个副本）
- 健康检查配置
- 配置和数据持久化

## 🔧 使用方式

### 方式1：直接在Python代码中使用
```python
from anomaly_detector import AnomalyDetector

detector = AnomalyDetector()
detector.load_data('your_data.csv')
detector.build_baseline()

result = detector.detect_real_time_anomaly(250.5, "2024-01-15 14:30:00")
if result['is_anomaly']:
    print("检测到异常！")
```

### 方式2：HTTP API服务
```bash
# 启动服务
python api_server.py

# 调用API
curl -X POST http://localhost:5000/api/v1/detect \
  -H "Content-Type: application/json" \
  -d '{"value": 250.5}'
```

### 方式3：Docker部署
```bash
# 构建镜像
docker build -t anomaly-detector .

# 运行容器
docker run -p 5000:5000 anomaly-detector
```

### 方式4：完整监控栈
```bash
# 启动完整系统
docker-compose up -d

# 访问Grafana
open http://localhost:3000
```

## 📊 性能指标

基于模拟测试的性能表现：

- **精确率**：90%（误报率低）
- **召回率**：79%（能发现大部分异常）
- **F1分数**：0.84（综合表现良好）
- **预测准确性**：MAPE 18.9%
- **响应时间**：<50ms（实时检测）

## 🛠️ 自定义和扩展

### 调整检测敏感度
```python
detector.alert_threshold = 0.6  # 更敏感
detector.alert_threshold = 0.9  # 不太敏感
```

### 扩展到多个指标
```python
detectors = {
    'response_time': AnomalyDetector(),
    'cpu_usage': AnomalyDetector(),
    'memory_usage': AnomalyDetector()
}
```

### 自定义告警处理
```python
def handle_anomaly(result):
    if result['severity'] == 'CRITICAL':
        send_sms_alert()
    elif result['severity'] == 'HIGH':
        send_email_alert()
    else:
        log_warning()
```

## 📚 文档

- **快速开始**：`快速开始指南.md` - 5分钟上手
- **详细文档**：`详细使用说明文档.md` - 完整技术细节
- **API文档**：查看 `api_server.py` 中的接口说明

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📄 许可证

MIT License - 可自由使用和修改
