#!/usr/bin/env python3
"""
时间序列预测器 - 使用多种算法进行API响应时间预测
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.metrics import mean_absolute_error, mean_squared_error
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import IsolationForest
import warnings
warnings.filterwarnings('ignore')

class TimeSeriesPredictor:
    def __init__(self):
        self.models = {}
        self.scalers = {}
        self.anomaly_detector = None
        
    def load_data(self, file_path):
        """加载时间序列数据"""
        self.data = pd.read_csv(file_path)
        self.data['ds'] = pd.to_datetime(self.data['ds'])
        self.data = self.data.sort_values('ds').reset_index(drop=True)
        print(f"数据加载完成，共 {len(self.data)} 个数据点")
        return self.data
    
    def split_data(self, train_ratio=0.8):
        """分割训练和测试数据"""
        split_idx = int(len(self.data) * train_ratio)
        self.train_data = self.data[:split_idx].copy()
        self.test_data = self.data[split_idx:].copy()
        
        print(f"训练数据: {len(self.train_data)} 个点")
        print(f"测试数据: {len(self.test_data)} 个点")
        
        return self.train_data, self.test_data
    
    def simple_moving_average(self, window=24):
        """简单移动平均预测"""
        print(f"\n=== 简单移动平均预测 (窗口={window}) ===")
        
        # 计算移动平均
        self.train_data['ma'] = self.train_data['y'].rolling(window=window).mean()
        
        # 预测：使用最后window个点的平均值
        last_values = self.train_data['y'].tail(window).values
        prediction = np.mean(last_values)
        
        # 为测试数据生成预测
        predictions = [prediction] * len(self.test_data)
        
        self.models['moving_average'] = {
            'predictions': predictions,
            'window': window,
            'last_mean': prediction
        }
        
        return predictions
    
    def exponential_smoothing(self, alpha=0.3):
        """指数平滑预测"""
        print(f"\n=== 指数平滑预测 (alpha={alpha}) ===")
        
        # 计算指数平滑
        smoothed = [self.train_data['y'].iloc[0]]
        
        for i in range(1, len(self.train_data)):
            smoothed_value = alpha * self.train_data['y'].iloc[i] + (1 - alpha) * smoothed[-1]
            smoothed.append(smoothed_value)
        
        # 预测：使用最后的平滑值
        last_smoothed = smoothed[-1]
        predictions = [last_smoothed] * len(self.test_data)
        
        self.models['exponential_smoothing'] = {
            'predictions': predictions,
            'alpha': alpha,
            'last_smoothed': last_smoothed,
            'smoothed_series': smoothed
        }
        
        return predictions
    
    def linear_trend_prediction(self):
        """线性趋势预测"""
        print(f"\n=== 线性趋势预测 ===")
        
        # 准备数据：时间作为x，响应时间作为y
        x = np.arange(len(self.train_data))
        y = self.train_data['y'].values
        
        # 计算线性回归系数
        A = np.vstack([x, np.ones(len(x))]).T
        slope, intercept = np.linalg.lstsq(A, y, rcond=None)[0]
        
        # 预测未来值
        future_x = np.arange(len(self.train_data), len(self.train_data) + len(self.test_data))
        predictions = slope * future_x + intercept
        
        self.models['linear_trend'] = {
            'predictions': predictions.tolist(),
            'slope': slope,
            'intercept': intercept
        }
        
        return predictions
    
    def seasonal_naive(self, season_length=24):
        """季节性朴素预测（假设24小时周期）"""
        print(f"\n=== 季节性朴素预测 (周期={season_length}) ===")
        
        predictions = []
        train_values = self.train_data['y'].values
        
        for i in range(len(self.test_data)):
            # 使用相同时间点的历史值
            seasonal_idx = (len(train_values) - season_length + (i % season_length)) % len(train_values)
            predictions.append(train_values[seasonal_idx])
        
        self.models['seasonal_naive'] = {
            'predictions': predictions,
            'season_length': season_length
        }
        
        return predictions
    
    def detect_anomalies(self, contamination=0.1):
        """异常检测"""
        print(f"\n=== 异常检测 ===")
        
        # 准备特征：响应时间 + 时间特征
        features = []
        for idx, row in self.data.iterrows():
            hour = row['ds'].hour
            weekday = row['ds'].weekday()
            features.append([row['y'], hour, weekday])
        
        features = np.array(features)
        
        # 训练异常检测器
        self.anomaly_detector = IsolationForest(contamination=contamination, random_state=42)
        anomaly_labels = self.anomaly_detector.fit_predict(features)
        
        # 标记异常点
        self.data['is_anomaly'] = anomaly_labels == -1
        anomaly_count = sum(self.data['is_anomaly'])
        
        print(f"检测到 {anomaly_count} 个异常点 ({anomaly_count/len(self.data)*100:.1f}%)")
        
        return self.data['is_anomaly']
    
    def evaluate_predictions(self):
        """评估所有模型的预测效果"""
        print(f"\n=== 模型评估 ===")
        
        actual = self.test_data['y'].values
        results = {}
        
        for model_name, model_data in self.models.items():
            predictions = np.array(model_data['predictions'])
            
            mae = mean_absolute_error(actual, predictions)
            rmse = np.sqrt(mean_squared_error(actual, predictions))
            mape = np.mean(np.abs((actual - predictions) / actual)) * 100
            
            results[model_name] = {
                'MAE': mae,
                'RMSE': rmse,
                'MAPE': mape
            }
            
            print(f"{model_name:20} | MAE: {mae:6.2f} | RMSE: {rmse:6.2f} | MAPE: {mape:5.1f}%")
        
        return results
    
    def plot_results(self, figsize=(15, 10)):
        """绘制预测结果"""
        fig, axes = plt.subplots(2, 2, figsize=figsize)
        fig.suptitle('时间序列预测结果对比', fontsize=16)
        
        # 准备数据
        train_time = self.train_data['ds']
        train_values = self.train_data['y']
        test_time = self.test_data['ds']
        test_values = self.test_data['y']
        
        model_names = list(self.models.keys())
        
        for i, (ax, model_name) in enumerate(zip(axes.flat, model_names)):
            # 绘制训练数据
            ax.plot(train_time, train_values, label='训练数据', alpha=0.7, color='blue')
            
            # 绘制测试数据
            ax.plot(test_time, test_values, label='实际值', color='green', linewidth=2)
            
            # 绘制预测数据
            predictions = self.models[model_name]['predictions']
            ax.plot(test_time, predictions, label='预测值', color='red', linewidth=2, linestyle='--')
            
            ax.set_title(f'{model_name}')
            ax.set_xlabel('时间')
            ax.set_ylabel('响应时间 (ms)')
            ax.legend()
            ax.grid(True, alpha=0.3)
            
            # 旋转x轴标签
            ax.tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        plt.show()
    
    def predict_next_hours(self, hours=24):
        """预测未来几小时的值"""
        print(f"\n=== 预测未来 {hours} 小时 ===")
        
        # 使用表现最好的模型（这里简单使用季节性朴素）
        if 'seasonal_naive' in self.models:
            season_length = self.models['seasonal_naive']['season_length']
            all_values = self.data['y'].values
            
            future_predictions = []
            for i in range(hours):
                seasonal_idx = (len(all_values) - season_length + (i % season_length)) % len(all_values)
                future_predictions.append(all_values[seasonal_idx])
            
            return future_predictions
        
        return None

def main():
    # 创建预测器
    predictor = TimeSeriesPredictor()
    
    # 加载数据
    data = predictor.load_data('api_response_time.csv')
    
    # 分割数据
    train_data, test_data = predictor.split_data(train_ratio=0.8)
    
    # 运行不同的预测算法
    predictor.simple_moving_average(window=24)
    predictor.exponential_smoothing(alpha=0.3)
    predictor.linear_trend_prediction()
    predictor.seasonal_naive(season_length=24)
    
    # 异常检测
    predictor.detect_anomalies(contamination=0.05)
    
    # 评估模型
    results = predictor.evaluate_predictions()
    
    # 绘制结果
    predictor.plot_results()
    
    # 预测未来
    future_pred = predictor.predict_next_hours(hours=12)
    if future_pred:
        print(f"\n未来12小时预测值: {[f'{x:.1f}' for x in future_pred[:6]]}...")

if __name__ == "__main__":
    main()
