# 🚀 快速开始指南 - 5分钟上手异常检测

## 第1步：运行基础示例（2分钟）

```bash
# 1. 进入项目目录
cd time_series_prediction

# 2. 生成测试数据
python generate_sample_data.py

# 3. 运行异常检测
python anomaly_detector.py
```

**你会看到类似这样的输出：**
```
🔍 启动API响应时间异常检测系统
==================================================
加载了 720 个历史数据点

=== 建立基线模型 (使用最近7天数据) ===
基线建立完成:
  整体平均响应时间: 136.4ms
  P95: 205.9ms
  P99: 213.0ms

检测到 0 个异常点

📈 下一小时预测:
  时间: 2025-07-29 14:33
  预期响应时间: 174.9ms
  正常范围: 124.8 - 225.0ms
```

## 第2步：测试异常检测效果（1分钟）

```bash
# 运行异常模拟测试
python simulate_anomalies.py
```

**你会看到检测结果：**
```
📊 检测结果分析:
  注入的异常数量: 33
  检测到的异常数量: 29

📈 性能指标:
  真阳性 (正确检测): 26
  假阳性 (误报): 3
  假阴性 (漏报): 7
  精确率: 0.90
  召回率: 0.79
  F1分数: 0.84
```

## 第3步：在你的代码中使用（2分钟）

### 最简单的使用方式

```python
from anomaly_detector import AnomalyDetector

# 1. 创建检测器
detector = AnomalyDetector()

# 2. 加载你的数据（CSV格式，包含时间和数值两列）
detector.load_data('your_data.csv')

# 3. 建立基线
detector.build_baseline(days_back=7)

# 4. 检测新数据
result = detector.detect_real_time_anomaly(
    current_value=250.5,  # 当前的响应时间
    current_time="2024-01-15 14:30:00"  # 当前时间
)

# 5. 处理结果
if result['is_anomaly']:
    print(f"🚨 异常！响应时间: {result['value']}ms")
    print(f"异常分数: {result['anomaly_score']:.2f}")
    print(f"严重程度: {result['severity']}")
else:
    print("✅ 正常")
```

### 数据格式要求

你的CSV文件需要这样的格式：
```csv
ds,y
2024-01-01 00:00:00,120.5
2024-01-01 01:00:00,115.2
2024-01-01 02:00:00,108.7
...
```

- `ds`: 时间列（必须叫ds）
- `y`: 数值列（必须叫y），比如响应时间、CPU使用率等

## 实际业务场景示例

### 场景1：监控API响应时间

```python
import time
import requests
from datetime import datetime
from anomaly_detector import AnomalyDetector

# 初始化
detector = AnomalyDetector()
detector.load_data('api_response_history.csv')  # 你的历史数据
detector.build_baseline()

# 持续监控
while True:
    # 测量API响应时间
    start = time.time()
    try:
        response = requests.get('http://your-api.com/health')
        response_time = (time.time() - start) * 1000  # 转换为毫秒
        
        # 检测异常
        result = detector.detect_real_time_anomaly(response_time, datetime.now())
        
        if result['is_anomaly']:
            # 发送告警（你需要实现这个函数）
            send_alert(f"API响应时间异常: {response_time:.1f}ms")
            
    except Exception as e:
        print(f"监控出错: {e}")
    
    time.sleep(60)  # 每分钟检测一次
```

### 场景2：分析历史数据中的异常

```python
import pandas as pd
from anomaly_detector import AnomalyDetector

# 加载数据
detector = AnomalyDetector()
data = detector.load_data('historical_data.csv')

# 用前80%数据建立基线
split_point = int(len(data) * 0.8)
detector.data = data[:split_point]
detector.build_baseline()

# 分析后20%数据
anomalies = []
for i in range(split_point, len(data)):
    row = data.iloc[i]
    result = detector.detect_real_time_anomaly(row['y'], row['ds'])
    if result['is_anomaly']:
        anomalies.append({
            'time': row['ds'],
            'value': row['y'],
            'score': result['anomaly_score']
        })

print(f"发现 {len(anomalies)} 个历史异常")
for anomaly in anomalies[:5]:  # 显示前5个
    print(f"时间: {anomaly['time']}, 值: {anomaly['value']}, 分数: {anomaly['score']:.2f}")
```

## 常用参数调整

### 调整敏感度

```python
# 更敏感（更容易报警）
detector.alert_threshold = 0.6  # 默认是0.8

# 不太敏感（减少误报）
detector.alert_threshold = 0.9
```

### 调整基线时间窗口

```python
# 使用更多历史数据建立基线
detector.build_baseline(days_back=14)  # 默认是7天

# 使用更少数据（适合数据变化快的场景）
detector.build_baseline(days_back=3)
```

## 集成到现有系统

### 方式1：HTTP API服务

```bash
# 启动API服务
python api_server.py
```

然后在你的应用中调用：
```python
import requests

response = requests.post('http://localhost:5000/api/v1/detect', 
                        json={'value': 250.5})
result = response.json()

if result['is_anomaly']:
    print("检测到异常！")
```

### 方式2：直接集成到Python应用

```python
# 在你的应用启动时
from anomaly_detector import AnomalyDetector

class YourApplication:
    def __init__(self):
        self.anomaly_detector = AnomalyDetector()
        self.anomaly_detector.load_data('your_data.csv')
        self.anomaly_detector.build_baseline()
    
    def process_metric(self, value):
        # 你的业务逻辑
        result = do_something(value)
        
        # 异常检测
        anomaly_result = self.anomaly_detector.detect_real_time_anomaly(value, datetime.now())
        if anomaly_result['is_anomaly']:
            self.handle_anomaly(anomaly_result)
        
        return result
```

## 故障排除

### 问题1：没有检测到异常
```python
# 检查数据质量
print(f"数据统计: {detector.data['y'].describe()}")
print(f"基线统计: {detector.baseline_stats}")

# 降低阈值
detector.alert_threshold = 0.6
```

### 问题2：误报太多
```python
# 提高阈值
detector.alert_threshold = 0.9

# 增加基线数据
detector.build_baseline(days_back=14)
```

### 问题3：数据格式错误
```python
# 检查数据格式
print(detector.data.head())
print(detector.data.dtypes)

# 确保时间列格式正确
detector.data['ds'] = pd.to_datetime(detector.data['ds'])
```

## 下一步

1. **阅读详细文档**：`详细使用说明文档.md` 包含所有函数的详细解释
2. **自定义配置**：根据你的业务场景调整参数
3. **集成告警**：连接到Slack、邮件或其他告警系统
4. **监控多个指标**：扩展到CPU、内存、错误率等多个指标

## 需要帮助？

- 查看 `详细使用说明文档.md` 获取完整的技术细节
- 运行 `simulate_anomalies.py` 了解检测效果
- 检查日志文件排查问题

现在你已经可以开始使用异常检测系统了！🎉
