#!/usr/bin/env python3
"""
实用的异常检测器 - 模拟真实的监控场景
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json

class AnomalyDetector:
    def __init__(self):
        self.baseline_stats = {}
        self.alert_threshold = 0.8  # 异常分数阈值
        
    def load_data(self, file_path):
        """加载历史数据"""
        self.data = pd.read_csv(file_path)
        self.data['ds'] = pd.to_datetime(self.data['ds'])
        self.data = self.data.sort_values('ds').reset_index(drop=True)
        print(f"加载了 {len(self.data)} 个历史数据点")
        return self.data
    
    def build_baseline(self, days_back=7):
        """建立基线模型"""
        print(f"\n=== 建立基线模型 (使用最近{days_back}天数据) ===")
        
        # 使用最近N天的数据建立基线
        cutoff_time = self.data['ds'].max() - timedelta(days=days_back)
        baseline_data = self.data[self.data['ds'] >= cutoff_time].copy()
        
        # 添加时间特征
        baseline_data['hour'] = baseline_data['ds'].dt.hour
        baseline_data['weekday'] = baseline_data['ds'].dt.weekday
        baseline_data['is_weekend'] = baseline_data['weekday'] >= 5
        
        # 计算不同时间段的统计信息
        self.baseline_stats = {}
        
        # 按小时统计
        hourly_stats = baseline_data.groupby('hour')['y'].agg(['mean', 'std']).to_dict()
        self.baseline_stats['hourly'] = {
            'mean': hourly_stats['mean'],
            'std': hourly_stats['std']
        }
        
        # 按工作日/周末统计
        weekend_stats = baseline_data.groupby('is_weekend')['y'].agg(['mean', 'std']).to_dict()
        self.baseline_stats['weekend'] = {
            'mean': weekend_stats['mean'],
            'std': weekend_stats['std']
        }
        
        # 整体统计
        self.baseline_stats['overall'] = {
            'mean': baseline_data['y'].mean(),
            'std': baseline_data['y'].std(),
            'p95': baseline_data['y'].quantile(0.95),
            'p99': baseline_data['y'].quantile(0.99)
        }
        
        print(f"基线建立完成:")
        print(f"  整体平均响应时间: {self.baseline_stats['overall']['mean']:.1f}ms")
        print(f"  P95: {self.baseline_stats['overall']['p95']:.1f}ms")
        print(f"  P99: {self.baseline_stats['overall']['p99']:.1f}ms")
        
        return self.baseline_stats
    
    def calculate_anomaly_score(self, value, timestamp):
        """计算异常分数 (0-1，越高越异常)"""
        dt = pd.to_datetime(timestamp)
        hour = dt.hour
        is_weekend = dt.weekday() >= 5
        
        # 获取对应时间段的基线
        hourly_mean = self.baseline_stats['hourly']['mean'].get(hour, self.baseline_stats['overall']['mean'])
        hourly_std = self.baseline_stats['hourly']['std'].get(hour, self.baseline_stats['overall']['std'])
        
        # 计算Z-score
        if hourly_std > 0:
            z_score = abs(value - hourly_mean) / hourly_std
        else:
            z_score = 0
        
        # 转换为0-1的异常分数
        anomaly_score = min(z_score / 3.0, 1.0)  # 3个标准差以上认为是完全异常
        
        return anomaly_score
    
    def detect_real_time_anomaly(self, current_value, current_time):
        """实时异常检测"""
        anomaly_score = self.calculate_anomaly_score(current_value, current_time)
        
        is_anomaly = anomaly_score >= self.alert_threshold
        
        # 生成告警信息
        alert_info = {
            'timestamp': current_time,
            'value': current_value,
            'anomaly_score': anomaly_score,
            'is_anomaly': is_anomaly,
            'severity': self._get_severity(anomaly_score),
            'expected_range': self._get_expected_range(current_time)
        }
        
        return alert_info
    
    def _get_severity(self, score):
        """根据异常分数确定严重程度"""
        if score >= 0.9:
            return "CRITICAL"
        elif score >= 0.8:
            return "HIGH"
        elif score >= 0.6:
            return "MEDIUM"
        else:
            return "LOW"
    
    def _get_expected_range(self, timestamp):
        """获取预期的正常范围"""
        dt = pd.to_datetime(timestamp)
        hour = dt.hour
        
        hourly_mean = self.baseline_stats['hourly']['mean'].get(hour, self.baseline_stats['overall']['mean'])
        hourly_std = self.baseline_stats['hourly']['std'].get(hour, self.baseline_stats['overall']['std'])
        
        return {
            'min': max(0, hourly_mean - 2 * hourly_std),
            'max': hourly_mean + 2 * hourly_std,
            'expected': hourly_mean
        }
    
    def batch_detect_anomalies(self, start_idx=600):
        """批量检测异常（模拟实时监控）"""
        print(f"\n=== 模拟实时异常检测 ===")
        
        anomalies = []
        
        # 从指定位置开始检测
        for i in range(start_idx, len(self.data)):
            row = self.data.iloc[i]
            alert_info = self.detect_real_time_anomaly(row['y'], row['ds'])
            
            if alert_info['is_anomaly']:
                anomalies.append(alert_info)
                print(f"🚨 异常检测: {alert_info['timestamp'].strftime('%Y-%m-%d %H:%M')} | "
                      f"值: {alert_info['value']:.1f}ms | "
                      f"分数: {alert_info['anomaly_score']:.2f} | "
                      f"严重程度: {alert_info['severity']}")
        
        print(f"\n检测到 {len(anomalies)} 个异常点")
        return anomalies
    
    def predict_next_hour(self, current_time):
        """预测下一小时的预期值"""
        next_hour = pd.to_datetime(current_time) + timedelta(hours=1)
        hour = next_hour.hour
        
        # 获取该小时的历史平均值
        expected_value = self.baseline_stats['hourly']['mean'].get(hour, self.baseline_stats['overall']['mean'])
        confidence_interval = self.baseline_stats['hourly']['std'].get(hour, self.baseline_stats['overall']['std']) * 2
        
        prediction = {
            'timestamp': next_hour,
            'predicted_value': expected_value,
            'confidence_interval': confidence_interval,
            'normal_range': {
                'min': max(0, expected_value - confidence_interval),
                'max': expected_value + confidence_interval
            }
        }
        
        return prediction
    
    def generate_monitoring_report(self):
        """生成监控报告"""
        print(f"\n=== 监控报告 ===")
        
        # 最近24小时的数据
        recent_data = self.data.tail(24)
        
        # 计算关键指标
        avg_response_time = recent_data['y'].mean()
        p95_response_time = recent_data['y'].quantile(0.95)
        max_response_time = recent_data['y'].max()
        
        # 异常检测
        anomaly_count = 0
        for _, row in recent_data.iterrows():
            score = self.calculate_anomaly_score(row['y'], row['ds'])
            if score >= self.alert_threshold:
                anomaly_count += 1
        
        report = {
            'period': '最近24小时',
            'metrics': {
                'avg_response_time': f"{avg_response_time:.1f}ms",
                'p95_response_time': f"{p95_response_time:.1f}ms", 
                'max_response_time': f"{max_response_time:.1f}ms",
                'anomaly_count': anomaly_count,
                'anomaly_rate': f"{anomaly_count/len(recent_data)*100:.1f}%"
            },
            'baseline_comparison': {
                'baseline_avg': f"{self.baseline_stats['overall']['mean']:.1f}ms",
                'current_vs_baseline': f"{((avg_response_time/self.baseline_stats['overall']['mean']-1)*100):+.1f}%"
            }
        }
        
        print(json.dumps(report, indent=2, ensure_ascii=False))
        return report

def simulate_real_time_monitoring():
    """模拟真实的实时监控场景"""
    print("🔍 启动API响应时间异常检测系统")
    print("=" * 50)
    
    # 初始化检测器
    detector = AnomalyDetector()
    
    # 加载历史数据
    detector.load_data('api_response_time.csv')
    
    # 建立基线
    detector.build_baseline(days_back=7)
    
    # 批量检测异常
    anomalies = detector.batch_detect_anomalies(start_idx=600)
    
    # 预测下一小时
    last_time = detector.data['ds'].iloc[-1]
    prediction = detector.predict_next_hour(last_time)
    
    print(f"\n📈 下一小时预测:")
    print(f"  时间: {prediction['timestamp'].strftime('%Y-%m-%d %H:%M')}")
    print(f"  预期响应时间: {prediction['predicted_value']:.1f}ms")
    print(f"  正常范围: {prediction['normal_range']['min']:.1f} - {prediction['normal_range']['max']:.1f}ms")
    
    # 生成监控报告
    detector.generate_monitoring_report()
    
    return detector, anomalies

if __name__ == "__main__":
    detector, anomalies = simulate_real_time_monitoring()
