#!/usr/bin/env python3
"""
生成模拟的API响应时间数据
模拟真实的业务场景：工作日vs周末，白天vs夜间的不同模式
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import matplotlib.pyplot as plt

def generate_api_response_time_data(days=30):
    """
    生成模拟的API响应时间数据
    
    模拟场景：
    - 基础响应时间：100ms
    - 工作日白天(9-18点)：流量高，响应时间增加
    - 周末：流量相对较低
    - 夜间(22-6点)：流量很低
    - 随机噪声：模拟真实环境的波动
    """
    
    # 生成时间序列（每小时一个数据点）
    start_date = datetime.now() - timedelta(days=days)
    dates = pd.date_range(start=start_date, periods=days*24, freq='H')
    
    response_times = []
    
    for date in dates:
        hour = date.hour
        weekday = date.weekday()  # 0=Monday, 6=Sunday
        
        # 基础响应时间
        base_time = 100
        
        # 时间因子（白天流量高）
        if 9 <= hour <= 18:
            time_factor = 1.5  # 白天流量高
        elif 19 <= hour <= 21:
            time_factor = 1.2  # 晚上中等流量
        else:
            time_factor = 0.8  # 夜间流量低
        
        # 工作日因子
        if weekday < 5:  # 工作日
            weekday_factor = 1.3
        else:  # 周末
            weekday_factor = 0.9
        
        # 计算响应时间
        response_time = base_time * time_factor * weekday_factor
        
        # 添加随机噪声
        noise = np.random.normal(0, 10)
        response_time += noise
        
        # 偶尔添加一些异常值（模拟系统故障）
        if np.random.random() < 0.02:  # 2%的概率出现异常
            response_time *= np.random.uniform(2, 4)  # 异常时响应时间增加2-4倍
        
        response_times.append(max(response_time, 20))  # 最小20ms
    
    # 创建DataFrame
    df = pd.DataFrame({
        'ds': dates,  # Prophet要求时间列名为'ds'
        'y': response_times  # Prophet要求目标列名为'y'
    })
    
    return df

def plot_data(df, title="API Response Time"):
    """绘制时间序列数据"""
    plt.figure(figsize=(15, 6))
    plt.plot(df['ds'], df['y'], linewidth=1, alpha=0.8)
    plt.title(title)
    plt.xlabel('Time')
    plt.ylabel('Response Time (ms)')
    plt.xticks(rotation=45)
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.show()

if __name__ == "__main__":
    # 生成30天的数据
    print("生成模拟数据...")
    data = generate_api_response_time_data(days=30)
    
    # 保存数据
    data.to_csv('api_response_time.csv', index=False)
    print(f"数据已保存到 api_response_time.csv")
    print(f"数据形状: {data.shape}")
    print(f"数据预览:")
    print(data.head())
    print(f"\n统计信息:")
    print(data['y'].describe())
    
    # 绘制数据
    plot_data(data)
