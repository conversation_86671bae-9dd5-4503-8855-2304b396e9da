#!/usr/bin/env python3
"""
模拟异常场景，测试异常检测效果
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from anomaly_detector import AnomalyDetector

def inject_anomalies(data, anomaly_scenarios):
    """向数据中注入异常"""
    modified_data = data.copy()
    injected_anomalies = []
    
    for scenario in anomaly_scenarios:
        start_idx = scenario['start_idx']
        duration = scenario['duration']
        anomaly_type = scenario['type']
        severity = scenario['severity']
        
        for i in range(start_idx, min(start_idx + duration, len(modified_data))):
            original_value = modified_data.iloc[i]['y']
            
            if anomaly_type == 'spike':
                # 响应时间突然增加
                modified_data.iloc[i, modified_data.columns.get_loc('y')] = original_value * severity
            elif anomaly_type == 'sustained_high':
                # 持续高响应时间
                modified_data.iloc[i, modified_data.columns.get_loc('y')] = original_value * severity
            elif anomaly_type == 'gradual_increase':
                # 逐渐增加
                factor = 1 + (severity - 1) * (i - start_idx) / duration
                modified_data.iloc[i, modified_data.columns.get_loc('y')] = original_value * factor
            
            injected_anomalies.append({
                'index': i,
                'timestamp': modified_data.iloc[i]['ds'],
                'original_value': original_value,
                'modified_value': modified_data.iloc[i]['y'],
                'type': anomaly_type,
                'severity': severity
            })
    
    return modified_data, injected_anomalies

def run_anomaly_simulation():
    """运行异常模拟测试"""
    print("🧪 异常检测模拟测试")
    print("=" * 50)
    
    # 加载原始数据
    detector = AnomalyDetector()
    original_data = detector.load_data('api_response_time.csv')
    
    # 定义异常场景
    anomaly_scenarios = [
        {
            'name': '数据库连接池耗尽',
            'start_idx': 650,
            'duration': 5,
            'type': 'spike',
            'severity': 3.0,  # 响应时间增加3倍
            'description': '模拟数据库连接池耗尽导致的响应时间激增'
        },
        {
            'name': '内存泄漏',
            'start_idx': 680,
            'duration': 20,
            'type': 'gradual_increase',
            'severity': 2.5,  # 逐渐增加到2.5倍
            'description': '模拟内存泄漏导致的性能逐渐下降'
        },
        {
            'name': '下游服务故障',
            'start_idx': 710,
            'duration': 8,
            'type': 'sustained_high',
            'severity': 4.0,  # 持续高响应时间
            'description': '模拟下游服务故障导致的持续高延迟'
        }
    ]
    
    # 注入异常
    print(f"\n📝 注入异常场景:")
    for scenario in anomaly_scenarios:
        print(f"  - {scenario['name']}: {scenario['description']}")
    
    modified_data, injected_anomalies = inject_anomalies(original_data, anomaly_scenarios)
    
    # 保存修改后的数据
    modified_data.to_csv('api_response_time_with_anomalies.csv', index=False)
    
    # 使用修改后的数据重新初始化检测器
    detector_with_anomalies = AnomalyDetector()
    detector_with_anomalies.data = modified_data
    
    # 建立基线（使用前600个正常数据点）
    baseline_data = modified_data[:600].copy()
    detector_with_anomalies.data = baseline_data
    detector_with_anomalies.build_baseline(days_back=7)
    
    # 恢复完整数据进行检测
    detector_with_anomalies.data = modified_data
    
    print(f"\n🔍 开始异常检测...")
    
    # 检测异常
    detected_anomalies = []
    for i in range(600, len(modified_data)):
        row = modified_data.iloc[i]
        alert_info = detector_with_anomalies.detect_real_time_anomaly(row['y'], row['ds'])
        
        if alert_info['is_anomaly']:
            detected_anomalies.append({
                'index': i,
                'timestamp': alert_info['timestamp'],
                'value': alert_info['value'],
                'anomaly_score': alert_info['anomaly_score'],
                'severity': alert_info['severity']
            })
    
    # 分析检测结果
    print(f"\n📊 检测结果分析:")
    print(f"  注入的异常数量: {len(injected_anomalies)}")
    print(f"  检测到的异常数量: {len(detected_anomalies)}")
    
    # 计算准确率
    true_positives = 0
    false_positives = 0
    
    injected_indices = {anomaly['index'] for anomaly in injected_anomalies}
    detected_indices = {anomaly['index'] for anomaly in detected_anomalies}
    
    true_positives = len(injected_indices & detected_indices)
    false_positives = len(detected_indices - injected_indices)
    false_negatives = len(injected_indices - detected_indices)
    
    precision = true_positives / (true_positives + false_positives) if (true_positives + false_positives) > 0 else 0
    recall = true_positives / (true_positives + false_negatives) if (true_positives + false_negatives) > 0 else 0
    f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0
    
    print(f"\n📈 性能指标:")
    print(f"  真阳性 (正确检测): {true_positives}")
    print(f"  假阳性 (误报): {false_positives}")
    print(f"  假阴性 (漏报): {false_negatives}")
    print(f"  精确率: {precision:.2f}")
    print(f"  召回率: {recall:.2f}")
    print(f"  F1分数: {f1_score:.2f}")
    
    # 详细展示检测到的异常
    print(f"\n🚨 检测到的异常详情:")
    for anomaly in detected_anomalies:
        timestamp = anomaly['timestamp'].strftime('%Y-%m-%d %H:%M')
        print(f"  {timestamp} | 值: {anomaly['value']:.1f}ms | "
              f"分数: {anomaly['anomaly_score']:.2f} | 严重程度: {anomaly['severity']}")
    
    # 展示注入的异常场景
    print(f"\n💉 注入的异常场景:")
    current_scenario = None
    for anomaly in sorted(injected_anomalies, key=lambda x: x['index']):
        # 确定属于哪个场景
        for scenario in anomaly_scenarios:
            if scenario['start_idx'] <= anomaly['index'] < scenario['start_idx'] + scenario['duration']:
                if current_scenario != scenario['name']:
                    print(f"\n  📍 {scenario['name']}:")
                    current_scenario = scenario['name']
                break
        
        timestamp = anomaly['timestamp'].strftime('%Y-%m-%d %H:%M')
        detected = "✅" if anomaly['index'] in detected_indices else "❌"
        print(f"    {detected} {timestamp} | "
              f"{anomaly['original_value']:.1f}ms → {anomaly['modified_value']:.1f}ms")
    
    return detector_with_anomalies, detected_anomalies, injected_anomalies

def test_prediction_accuracy():
    """测试预测准确性"""
    print(f"\n🎯 预测准确性测试")
    print("=" * 30)
    
    detector = AnomalyDetector()
    data = detector.load_data('api_response_time.csv')
    
    # 使用前80%的数据建立基线
    split_idx = int(len(data) * 0.8)
    train_data = data[:split_idx].copy()
    test_data = data[split_idx:].copy()
    
    detector.data = train_data
    detector.build_baseline(days_back=7)
    
    # 预测测试数据
    predictions = []
    actuals = []
    
    for _, row in test_data.iterrows():
        prediction = detector.predict_next_hour(row['ds'] - timedelta(hours=1))
        predictions.append(prediction['predicted_value'])
        actuals.append(row['y'])
    
    # 计算预测误差
    mae = np.mean(np.abs(np.array(predictions) - np.array(actuals)))
    rmse = np.sqrt(np.mean((np.array(predictions) - np.array(actuals))**2))
    mape = np.mean(np.abs((np.array(actuals) - np.array(predictions)) / np.array(actuals))) * 100
    
    print(f"预测性能:")
    print(f"  平均绝对误差 (MAE): {mae:.2f}ms")
    print(f"  均方根误差 (RMSE): {rmse:.2f}ms") 
    print(f"  平均绝对百分比误差 (MAPE): {mape:.1f}%")

if __name__ == "__main__":
    # 运行异常模拟
    detector, detected, injected = run_anomaly_simulation()
    
    # 测试预测准确性
    test_prediction_accuracy()
