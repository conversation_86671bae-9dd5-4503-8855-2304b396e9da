# 时间序列异常检测系统 - 超详细使用说明

## 📖 目录
1. [系统概述](#系统概述)
2. [核心类详解](#核心类详解)
3. [函数命名解释](#函数命名解释)
4. [完整调用链路](#完整调用链路)
5. [实际使用示例](#实际使用示例)
6. [集成到现有系统](#集成到现有系统)
7. [常见问题解答](#常见问题解答)

## 系统概述

### 这个系统是干什么的？
这是一个**API响应时间异常检测系统**，专门用来监控你的服务是否出现性能问题。

**核心功能**：
- 📊 **学习正常模式**：分析历史数据，知道什么是"正常"的响应时间
- 🚨 **实时异常检测**：新数据进来时，立即判断是否异常
- 📈 **预测下一小时**：基于历史模式预测未来的响应时间
- 📋 **生成监控报告**：汇总最近的性能指标

### 为什么叫AnomalyDetector？
- **Anomaly** = 异常、反常
- **Detector** = 检测器
- 合起来就是"异常检测器"，专门用来找出不正常的数据点

## 核心类详解

### AnomalyDetector类的设计思路

```python
class AnomalyDetector:
    def __init__(self):
        self.baseline_stats = {}      # 基线统计信息
        self.alert_threshold = 0.8    # 告警阈值
```

**为什么这样设计？**
1. `baseline_stats`：存储"正常情况"的统计数据，就像医生知道正常人的体温范围
2. `alert_threshold`：设定告警的敏感度，0.8意思是异常分数超过80%就告警

## 函数命名解释

### 1. `load_data(self, file_path)`

**命名含义**：
- `load` = 加载、装载
- `data` = 数据
- 合起来就是"加载数据"

**为什么叫这个名字？**
因为这个函数的职责就是从CSV文件中读取历史数据，就像把货物装到卡车上一样。

**函数内部做了什么？**
```python
def load_data(self, file_path):
    # 1. 读取CSV文件
    self.data = pd.read_csv(file_path)
    
    # 2. 转换时间格式（pandas能理解的时间格式）
    self.data['ds'] = pd.to_datetime(self.data['ds'])
    
    # 3. 按时间排序（确保数据是按时间顺序的）
    self.data = self.data.sort_values('ds').reset_index(drop=True)
    
    # 4. 打印加载结果
    print(f"加载了 {len(self.data)} 个历史数据点")
    return self.data
```

### 2. `build_baseline(self, days_back=7)`

**命名含义**：
- `build` = 建造、构建
- `baseline` = 基线、基准线
- 合起来就是"构建基准线"

**为什么叫baseline？**
在医学中，baseline是指治疗前的基础状态。在监控中，baseline是指系统正常运行时的基准状态。

**这个函数在干什么？**
```python
def build_baseline(self, days_back=7):
    # 1. 选择最近N天的数据作为"正常"样本
    cutoff_time = self.data['ds'].max() - timedelta(days=days_back)
    baseline_data = self.data[self.data['ds'] >= cutoff_time].copy()
    
    # 2. 添加时间特征（因为不同时间段的正常值不同）
    baseline_data['hour'] = baseline_data['ds'].dt.hour        # 几点钟
    baseline_data['weekday'] = baseline_data['ds'].dt.weekday  # 星期几
    baseline_data['is_weekend'] = baseline_data['weekday'] >= 5 # 是否周末
    
    # 3. 计算各种统计信息
    # 按小时统计：知道每个小时的正常响应时间范围
    hourly_stats = baseline_data.groupby('hour')['y'].agg(['mean', 'std'])
    
    # 按工作日/周末统计：周末和工作日的模式不同
    weekend_stats = baseline_data.groupby('is_weekend')['y'].agg(['mean', 'std'])
    
    # 整体统计：总体的正常范围
    overall_stats = {
        'mean': baseline_data['y'].mean(),    # 平均值
        'std': baseline_data['y'].std(),      # 标准差
        'p95': baseline_data['y'].quantile(0.95),  # 95%分位数
        'p99': baseline_data['y'].quantile(0.99)   # 99%分位数
    }
```

**为什么要分这么多维度统计？**
因为API响应时间有明显的时间模式：
- 白天流量大，响应时间长
- 夜间流量小，响应时间短
- 工作日和周末模式不同

### 3. `calculate_anomaly_score(self, value, timestamp)`

**命名含义**：
- `calculate` = 计算
- `anomaly` = 异常
- `score` = 分数、得分
- 合起来就是"计算异常分数"

**为什么要计算分数而不是直接判断异常？**
因为异常不是非黑即白的，而是有程度的：
- 分数0.3：轻微异常，可能是正常波动
- 分数0.8：明显异常，需要关注
- 分数0.95：严重异常，立即处理

**计算逻辑详解**：
```python
def calculate_anomaly_score(self, value, timestamp):
    # 1. 解析时间信息
    dt = pd.to_datetime(timestamp)
    hour = dt.hour                    # 当前是几点
    is_weekend = dt.weekday() >= 5    # 是否周末
    
    # 2. 获取对应时间段的"正常"基准
    # 比如：如果现在是下午2点，就找下午2点的历史平均值
    hourly_mean = self.baseline_stats['hourly']['mean'].get(hour, 
                    self.baseline_stats['overall']['mean'])
    hourly_std = self.baseline_stats['hourly']['std'].get(hour, 
                    self.baseline_stats['overall']['std'])
    
    # 3. 计算Z-score（统计学概念）
    # Z-score表示当前值偏离平均值多少个标准差
    if hourly_std > 0:
        z_score = abs(value - hourly_mean) / hourly_std
    else:
        z_score = 0
    
    # 4. 转换为0-1的异常分数
    # 3个标准差以上认为是完全异常（分数=1.0）
    anomaly_score = min(z_score / 3.0, 1.0)
    
    return anomaly_score
```

**为什么用Z-score？**
Z-score是统计学中衡量数据点偏离正常范围程度的标准方法：
- Z-score = 0：完全正常
- Z-score = 1：偏离1个标准差
- Z-score = 2：偏离2个标准差（比较异常）
- Z-score = 3：偏离3个标准差（非常异常）

### 4. `detect_real_time_anomaly(self, current_value, current_time)`

**命名含义**：
- `detect` = 检测、发现
- `real_time` = 实时
- `anomaly` = 异常
- 合起来就是"实时异常检测"

**为什么强调real_time？**
因为在生产环境中，我们需要在问题发生的第一时间就发现，而不是事后分析。

**函数调用链路**：
```python
def detect_real_time_anomaly(self, current_value, current_time):
    # 第1步：计算异常分数
    anomaly_score = self.calculate_anomaly_score(current_value, current_time)
    
    # 第2步：判断是否需要告警
    is_anomaly = anomaly_score >= self.alert_threshold
    
    # 第3步：生成完整的告警信息
    alert_info = {
        'timestamp': current_time,           # 什么时候发生的
        'value': current_value,              # 具体的值是多少
        'anomaly_score': anomaly_score,      # 异常分数
        'is_anomaly': is_anomaly,            # 是否需要告警
        'severity': self._get_severity(anomaly_score),  # 严重程度
        'expected_range': self._get_expected_range(current_time)  # 正常范围
    }
    
    return alert_info
```

### 5. `_get_severity(self, score)` 和 `_get_expected_range(self, timestamp)`

**为什么函数名前面有下划线？**
在Python中，下划线开头的函数表示"私有函数"，意思是这个函数只在类内部使用，外部不应该直接调用。

**`_get_severity`的逻辑**：
```python
def _get_severity(self, score):
    if score >= 0.9:
        return "CRITICAL"    # 紧急：立即处理
    elif score >= 0.8:
        return "HIGH"        # 高：30分钟内处理
    elif score >= 0.6:
        return "MEDIUM"      # 中：1小时内处理
    else:
        return "LOW"         # 低：关注即可
```

## 完整调用链路

### 典型使用场景的完整流程

```python
# 第1步：创建检测器实例
detector = AnomalyDetector()

# 第2步：加载历史数据
detector.load_data('api_response_time.csv')
# 内部调用链：
# load_data() -> pd.read_csv() -> pd.to_datetime() -> sort_values()

# 第3步：建立基线模型
detector.build_baseline(days_back=7)
# 内部调用链：
# build_baseline() -> 数据筛选 -> 添加时间特征 -> groupby统计 -> 保存到baseline_stats

# 第4步：实时检测（模拟新数据到来）
new_value = 250.5  # 新的响应时间
new_time = "2024-01-15 14:30:00"  # 当前时间

alert_info = detector.detect_real_time_anomaly(new_value, new_time)
# 内部调用链：
# detect_real_time_anomaly() 
#   -> calculate_anomaly_score() 
#     -> 解析时间 -> 获取基准值 -> 计算Z-score -> 转换为异常分数
#   -> 判断是否告警
#   -> _get_severity() -> 确定严重程度
#   -> _get_expected_range() -> 获取正常范围
#   -> 组装告警信息

# 第5步：处理告警结果
if alert_info['is_anomaly']:
    print(f"🚨 检测到异常！")
    print(f"时间: {alert_info['timestamp']}")
    print(f"响应时间: {alert_info['value']}ms")
    print(f"异常分数: {alert_info['anomaly_score']:.2f}")
    print(f"严重程度: {alert_info['severity']}")
    print(f"正常范围: {alert_info['expected_range']['min']:.1f} - {alert_info['expected_range']['max']:.1f}ms")
```

### 数据流向图

```
原始CSV数据 
    ↓ (load_data)
pandas DataFrame 
    ↓ (build_baseline)
基线统计信息 (baseline_stats)
    ↓ (新数据到来)
实时数据点 
    ↓ (calculate_anomaly_score)
异常分数 
    ↓ (detect_real_time_anomaly)
告警信息 
    ↓ (业务逻辑)
告警处理/报告生成
```

## 实际使用示例

### 场景1：监控电商网站API

```python
# 1. 初始化
detector = AnomalyDetector()

# 2. 加载过去30天的API响应时间数据
detector.load_data('/var/log/api_response_times.csv')

# 3. 用最近7天的数据建立基线（排除异常时期）
detector.build_baseline(days_back=7)

# 4. 在实际监控中，每分钟检测一次
import time
while True:
    # 获取当前API响应时间（这里需要你自己实现获取逻辑）
    current_response_time = get_current_api_response_time()  # 假设这个函数存在
    current_time = datetime.now()
    
    # 检测异常
    alert = detector.detect_real_time_anomaly(current_response_time, current_time)
    
    # 如果检测到异常，发送告警
    if alert['is_anomaly']:
        send_alert_to_slack(alert)  # 发送到Slack
        trigger_auto_scaling()     # 触发自动扩容
    
    # 等待1分钟
    time.sleep(60)
```

### 场景2：集成到Prometheus监控

```python
from prometheus_client import Gauge, start_http_server

# 创建Prometheus指标
anomaly_score_gauge = Gauge('api_anomaly_score', 'API响应时间异常分数')
is_anomaly_gauge = Gauge('api_is_anomaly', 'API是否异常 (1=异常, 0=正常)')

def prometheus_monitoring():
    detector = AnomalyDetector()
    detector.load_data('api_response_time.csv')
    detector.build_baseline()
    
    # 启动Prometheus HTTP服务器
    start_http_server(8000)
    
    while True:
        # 获取当前指标
        current_value = get_api_response_time()
        current_time = datetime.now()
        
        # 检测异常
        alert = detector.detect_real_time_anomaly(current_value, current_time)
        
        # 更新Prometheus指标
        anomaly_score_gauge.set(alert['anomaly_score'])
        is_anomaly_gauge.set(1 if alert['is_anomaly'] else 0)
        
        time.sleep(30)  # 每30秒检测一次
```

### 场景3：批量分析历史数据

```python
def analyze_historical_anomalies():
    detector = AnomalyDetector()
    data = detector.load_data('api_response_time.csv')
    
    # 用前80%的数据建立基线
    split_point = int(len(data) * 0.8)
    detector.data = data[:split_point]
    detector.build_baseline()
    
    # 分析后20%的数据
    anomalies = []
    for i in range(split_point, len(data)):
        row = data.iloc[i]
        alert = detector.detect_real_time_anomaly(row['y'], row['ds'])
        if alert['is_anomaly']:
            anomalies.append(alert)
    
    # 生成分析报告
    print(f"分析了 {len(data) - split_point} 个数据点")
    print(f"发现 {len(anomalies)} 个异常")
    print(f"异常率: {len(anomalies)/(len(data) - split_point)*100:.1f}%")
    
    return anomalies
```

## 集成到现有系统

### 1. 集成到Spring Boot应用

```java
// Java代码示例
@RestController
public class MonitoringController {
    
    @Autowired
    private PythonScriptRunner pythonRunner;
    
    @PostMapping("/api/check-anomaly")
    public ResponseEntity<AnomalyResult> checkAnomaly(@RequestBody MetricData data) {
        // 调用Python异常检测脚本
        String pythonScript = "python /path/to/anomaly_detector.py";
        String result = pythonRunner.execute(pythonScript, data.toJson());
        
        AnomalyResult anomalyResult = parseResult(result);
        
        if (anomalyResult.isAnomaly()) {
            // 发送告警
            alertService.sendAlert(anomalyResult);
        }
        
        return ResponseEntity.ok(anomalyResult);
    }
}
```

### 2. 集成到Grafana

```python
# 创建一个HTTP API服务
from flask import Flask, request, jsonify

app = Flask(__name__)
detector = AnomalyDetector()

@app.route('/api/anomaly-detection', methods=['POST'])
def detect_anomaly():
    data = request.json
    value = data['value']
    timestamp = data['timestamp']
    
    alert = detector.detect_real_time_anomaly(value, timestamp)
    
    return jsonify({
        'anomaly_score': alert['anomaly_score'],
        'is_anomaly': alert['is_anomaly'],
        'severity': alert['severity'],
        'expected_range': alert['expected_range']
    })

if __name__ == '__main__':
    detector.load_data('api_response_time.csv')
    detector.build_baseline()
    app.run(host='0.0.0.0', port=5000)
```

然后在Grafana中配置HTTP数据源，调用这个API。

### 3. 集成到Kubernetes

```yaml
# deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: anomaly-detector
spec:
  replicas: 1
  selector:
    matchLabels:
      app: anomaly-detector
  template:
    metadata:
      labels:
        app: anomaly-detector
    spec:
      containers:
      - name: anomaly-detector
        image: your-registry/anomaly-detector:latest
        ports:
        - containerPort: 5000
        env:
        - name: DATA_PATH
          value: "/data/api_response_time.csv"
        volumeMounts:
        - name: data-volume
          mountPath: /data
      volumes:
      - name: data-volume
        persistentVolumeClaim:
          claimName: monitoring-data-pvc
```

## 常见问题解答

### Q1: 为什么异常检测不准确？

**A1**: 可能的原因和解决方案：

1. **基线数据不够**
   ```python
   # 增加基线数据天数
   detector.build_baseline(days_back=14)  # 从7天增加到14天
   ```

2. **阈值设置不合适**
   ```python
   # 调整告警阈值
   detector.alert_threshold = 0.7  # 从0.8降低到0.7，更敏感
   ```

3. **数据质量问题**
   ```python
   # 检查数据质量
   print(f"数据缺失值: {detector.data.isnull().sum()}")
   print(f"数据统计: {detector.data.describe()}")
   ```

### Q2: 如何处理季节性数据？

**A2**: 当前版本只考虑了日周期，如果有月度或季度周期：

```python
def build_enhanced_baseline(self, days_back=30):
    # 添加更多时间特征
    baseline_data['day_of_month'] = baseline_data['ds'].dt.day
    baseline_data['month'] = baseline_data['ds'].dt.month
    
    # 按月份统计
    monthly_stats = baseline_data.groupby('month')['y'].agg(['mean', 'std'])
    self.baseline_stats['monthly'] = monthly_stats.to_dict()
```

### Q3: 如何减少误报？

**A3**: 几种策略：

1. **增加确认机制**
   ```python
   def confirm_anomaly(self, values, timestamps):
       # 连续3个点都异常才告警
       anomaly_count = 0
       for value, timestamp in zip(values, timestamps):
           if self.detect_real_time_anomaly(value, timestamp)['is_anomaly']:
               anomaly_count += 1
       return anomaly_count >= 3
   ```

2. **动态调整阈值**
   ```python
   def adaptive_threshold(self, recent_false_positive_rate):
       if recent_false_positive_rate > 0.1:  # 误报率超过10%
           self.alert_threshold += 0.05  # 提高阈值
   ```

### Q4: 如何扩展到多个指标？

**A4**: 创建多指标检测器：

```python
class MultiMetricAnomalyDetector:
    def __init__(self):
        self.detectors = {
            'response_time': AnomalyDetector(),
            'cpu_usage': AnomalyDetector(),
            'memory_usage': AnomalyDetector(),
            'error_rate': AnomalyDetector()
        }
    
    def detect_multi_metric_anomaly(self, metrics, timestamp):
        results = {}
        for metric_name, value in metrics.items():
            if metric_name in self.detectors:
                results[metric_name] = self.detectors[metric_name].detect_real_time_anomaly(value, timestamp)
        return results
```

## 详细集成步骤

### 步骤1：准备Python环境

```bash
# 1. 创建虚拟环境
python -m venv anomaly_detection_env

# 2. 激活虚拟环境
source anomaly_detection_env/bin/activate  # Linux/Mac
# 或者
anomaly_detection_env\Scripts\activate     # Windows

# 3. 安装依赖
pip install pandas numpy scikit-learn flask

# 4. 测试安装
python -c "import pandas; print('安装成功')"
```

### 步骤2：创建生产环境的检测服务

```python
# production_detector.py
import os
import json
import logging
from datetime import datetime
from anomaly_detector import AnomalyDetector

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ProductionAnomalyDetector:
    def __init__(self, config_file='detector_config.json'):
        """
        生产环境的异常检测器

        为什么叫ProductionAnomalyDetector？
        - Production：生产环境，表示这是用于实际业务的版本
        - 与开发测试版本区别：增加了配置管理、日志记录、错误处理
        """
        self.config = self._load_config(config_file)
        self.detector = AnomalyDetector()
        self.is_initialized = False

    def _load_config(self, config_file):
        """
        加载配置文件

        为什么需要配置文件？
        - 不同环境（开发/测试/生产）的参数不同
        - 可以不重启服务就调整参数
        """
        try:
            with open(config_file, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            logger.warning(f"配置文件 {config_file} 不存在，使用默认配置")
            return {
                "data_file": "api_response_time.csv",
                "baseline_days": 7,
                "alert_threshold": 0.8,
                "update_interval_hours": 24
            }

    def initialize(self):
        """
        初始化检测器

        为什么单独一个initialize函数？
        - 初始化可能耗时较长（加载大量历史数据）
        - 可以在服务启动时异步初始化
        - 初始化失败不影响服务启动
        """
        try:
            logger.info("开始初始化异常检测器...")

            # 检查数据文件是否存在
            data_file = self.config['data_file']
            if not os.path.exists(data_file):
                raise FileNotFoundError(f"数据文件 {data_file} 不存在")

            # 加载数据
            self.detector.load_data(data_file)

            # 建立基线
            self.detector.build_baseline(days_back=self.config['baseline_days'])

            # 设置阈值
            self.detector.alert_threshold = self.config['alert_threshold']

            self.is_initialized = True
            logger.info("异常检测器初始化完成")

        except Exception as e:
            logger.error(f"初始化失败: {str(e)}")
            raise

    def detect(self, value, timestamp=None):
        """
        检测异常

        参数说明：
        - value: 要检测的数值（如响应时间）
        - timestamp: 时间戳，如果不提供则使用当前时间

        返回值：包含检测结果的字典
        """
        if not self.is_initialized:
            raise RuntimeError("检测器未初始化，请先调用 initialize()")

        if timestamp is None:
            timestamp = datetime.now()

        try:
            result = self.detector.detect_real_time_anomaly(value, timestamp)

            # 记录检测日志
            if result['is_anomaly']:
                logger.warning(f"检测到异常: 值={value}, 分数={result['anomaly_score']:.2f}")
            else:
                logger.debug(f"正常检测: 值={value}, 分数={result['anomaly_score']:.2f}")

            return result

        except Exception as e:
            logger.error(f"检测过程出错: {str(e)}")
            # 返回安全的默认值
            return {
                'timestamp': timestamp,
                'value': value,
                'anomaly_score': 0.0,
                'is_anomaly': False,
                'severity': 'UNKNOWN',
                'error': str(e)
            }
```

### 步骤3：创建HTTP API服务

```python
# api_server.py
from flask import Flask, request, jsonify
from production_detector import ProductionAnomalyDetector
import threading
import time

app = Flask(__name__)

# 全局检测器实例
detector = None

def initialize_detector():
    """
    在后台线程中初始化检测器

    为什么用后台线程？
    - 初始化可能需要几秒钟，不能阻塞API服务启动
    - 即使初始化失败，API服务也能正常启动并返回错误信息
    """
    global detector
    try:
        detector = ProductionAnomalyDetector()
        detector.initialize()
        print("✅ 异常检测器初始化成功")
    except Exception as e:
        print(f"❌ 异常检测器初始化失败: {e}")

@app.route('/health', methods=['GET'])
def health_check():
    """
    健康检查接口

    为什么需要健康检查？
    - Kubernetes等容器编排系统需要知道服务是否正常
    - 负载均衡器需要知道哪些实例可以接收流量
    """
    if detector and detector.is_initialized:
        return jsonify({"status": "healthy", "detector": "ready"})
    else:
        return jsonify({"status": "unhealthy", "detector": "not_ready"}), 503

@app.route('/api/v1/detect', methods=['POST'])
def detect_anomaly():
    """
    异常检测API

    请求格式：
    {
        "value": 250.5,
        "timestamp": "2024-01-15T14:30:00Z"  // 可选
    }

    响应格式：
    {
        "anomaly_score": 0.85,
        "is_anomaly": true,
        "severity": "HIGH",
        "expected_range": {"min": 100, "max": 200}
    }
    """
    if not detector or not detector.is_initialized:
        return jsonify({
            "error": "检测器未就绪",
            "code": "DETECTOR_NOT_READY"
        }), 503

    try:
        # 解析请求
        data = request.json
        if not data or 'value' not in data:
            return jsonify({
                "error": "缺少必需参数 'value'",
                "code": "MISSING_PARAMETER"
            }), 400

        value = float(data['value'])
        timestamp = data.get('timestamp')  # 可选参数

        # 执行检测
        result = detector.detect(value, timestamp)

        # 移除内部字段，只返回必要信息
        response = {
            "anomaly_score": result['anomaly_score'],
            "is_anomaly": result['is_anomaly'],
            "severity": result['severity'],
            "expected_range": result['expected_range']
        }

        return jsonify(response)

    except ValueError:
        return jsonify({
            "error": "参数 'value' 必须是数字",
            "code": "INVALID_VALUE_TYPE"
        }), 400
    except Exception as e:
        return jsonify({
            "error": f"内部错误: {str(e)}",
            "code": "INTERNAL_ERROR"
        }), 500

@app.route('/api/v1/batch-detect', methods=['POST'])
def batch_detect():
    """
    批量检测API

    请求格式：
    {
        "data": [
            {"value": 250.5, "timestamp": "2024-01-15T14:30:00Z"},
            {"value": 180.2, "timestamp": "2024-01-15T14:31:00Z"}
        ]
    }
    """
    if not detector or not detector.is_initialized:
        return jsonify({"error": "检测器未就绪"}), 503

    try:
        data = request.json
        if not data or 'data' not in data:
            return jsonify({"error": "缺少参数 'data'"}), 400

        results = []
        for item in data['data']:
            result = detector.detect(item['value'], item.get('timestamp'))
            results.append({
                "value": item['value'],
                "anomaly_score": result['anomaly_score'],
                "is_anomaly": result['is_anomaly'],
                "severity": result['severity']
            })

        return jsonify({"results": results})

    except Exception as e:
        return jsonify({"error": str(e)}), 500

if __name__ == '__main__':
    # 启动后台初始化
    init_thread = threading.Thread(target=initialize_detector)
    init_thread.daemon = True
    init_thread.start()

    # 启动API服务
    app.run(host='0.0.0.0', port=5000, debug=False)
```

### 步骤4：创建配置文件

```json
// detector_config.json
{
    "data_file": "/data/api_response_time.csv",
    "baseline_days": 7,
    "alert_threshold": 0.8,
    "update_interval_hours": 24,
    "logging": {
        "level": "INFO",
        "file": "/var/log/anomaly_detector.log"
    },
    "alerts": {
        "webhook_url": "https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK",
        "email_recipients": ["<EMAIL>"]
    }
}
```

### 步骤5：集成到现有监控系统

#### 5.1 集成到Prometheus + Grafana

```python
# prometheus_exporter.py
from prometheus_client import Gauge, Counter, start_http_server
from production_detector import ProductionAnomalyDetector
import time
import requests

# 定义Prometheus指标
anomaly_score = Gauge('api_anomaly_score', 'API响应时间异常分数')
anomaly_detected = Counter('api_anomaly_total', 'API异常检测总数', ['severity'])
detection_duration = Gauge('anomaly_detection_duration_seconds', '异常检测耗时')

class PrometheusExporter:
    def __init__(self):
        self.detector = ProductionAnomalyDetector()
        self.detector.initialize()

    def collect_metrics(self):
        """
        从你的API获取当前响应时间并检测异常
        """
        try:
            # 这里需要替换为你实际获取API响应时间的方法
            current_response_time = self.get_current_api_response_time()

            start_time = time.time()
            result = self.detector.detect(current_response_time)
            detection_time = time.time() - start_time

            # 更新Prometheus指标
            anomaly_score.set(result['anomaly_score'])
            detection_duration.set(detection_time)

            if result['is_anomaly']:
                anomaly_detected.labels(severity=result['severity']).inc()

        except Exception as e:
            print(f"指标收集失败: {e}")

    def get_current_api_response_time(self):
        """
        获取当前API响应时间
        这里需要根据你的实际情况实现
        """
        try:
            # 示例：调用你的API并测量响应时间
            start = time.time()
            response = requests.get('http://your-api.com/health', timeout=5)
            end = time.time()

            if response.status_code == 200:
                return (end - start) * 1000  # 转换为毫秒
            else:
                return 5000  # 错误时返回高响应时间

        except requests.RequestException:
            return 10000  # 超时或连接失败

def main():
    exporter = PrometheusExporter()

    # 启动Prometheus HTTP服务器
    start_http_server(8000)
    print("Prometheus exporter started on port 8000")

    # 定期收集指标
    while True:
        exporter.collect_metrics()
        time.sleep(30)  # 每30秒收集一次

if __name__ == '__main__':
    main()
```

#### 5.2 Grafana Dashboard配置

```json
// grafana_dashboard.json
{
  "dashboard": {
    "title": "API异常检测监控",
    "panels": [
      {
        "title": "异常分数",
        "type": "stat",
        "targets": [
          {
            "expr": "api_anomaly_score",
            "legendFormat": "异常分数"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "thresholds": {
              "steps": [
                {"color": "green", "value": 0},
                {"color": "yellow", "value": 0.6},
                {"color": "red", "value": 0.8}
              ]
            }
          }
        }
      },
      {
        "title": "异常检测历史",
        "type": "timeseries",
        "targets": [
          {
            "expr": "api_anomaly_score",
            "legendFormat": "异常分数"
          }
        ]
      }
    ]
  }
}
```

### 步骤6：部署到生产环境

#### 6.1 Docker化部署

```dockerfile
# Dockerfile
FROM python:3.9-slim

WORKDIR /app

# 安装依赖
COPY requirements.txt .
RUN pip install -r requirements.txt

# 复制代码
COPY . .

# 创建数据目录
RUN mkdir -p /data /var/log

# 暴露端口
EXPOSE 5000 8000

# 启动命令
CMD ["python", "api_server.py"]
```

```yaml
# docker-compose.yml
version: '3.8'
services:
  anomaly-detector:
    build: .
    ports:
      - "5000:5000"
      - "8000:8000"
    volumes:
      - ./data:/data
      - ./logs:/var/log
    environment:
      - CONFIG_FILE=/app/detector_config.json
    restart: unless-stopped

  prometheus:
    image: prom/prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'

  grafana:
    image: grafana/grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana-storage:/var/lib/grafana

volumes:
  grafana-storage:
```

#### 6.2 Kubernetes部署

```yaml
# k8s-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: anomaly-detector
  labels:
    app: anomaly-detector
spec:
  replicas: 2
  selector:
    matchLabels:
      app: anomaly-detector
  template:
    metadata:
      labels:
        app: anomaly-detector
    spec:
      containers:
      - name: anomaly-detector
        image: your-registry/anomaly-detector:latest
        ports:
        - containerPort: 5000
          name: api
        - containerPort: 8000
          name: metrics
        env:
        - name: CONFIG_FILE
          value: "/config/detector_config.json"
        volumeMounts:
        - name: config
          mountPath: /config
        - name: data
          mountPath: /data
        livenessProbe:
          httpGet:
            path: /health
            port: 5000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 5000
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: config
        configMap:
          name: detector-config
      - name: data
        persistentVolumeClaim:
          claimName: detector-data-pvc

---
apiVersion: v1
kind: Service
metadata:
  name: anomaly-detector-service
spec:
  selector:
    app: anomaly-detector
  ports:
  - name: api
    port: 5000
    targetPort: 5000
  - name: metrics
    port: 8000
    targetPort: 8000
```

### 使用示例

#### 调用API检测异常

```bash
# 单次检测
curl -X POST http://localhost:5000/api/v1/detect \
  -H "Content-Type: application/json" \
  -d '{"value": 250.5}'

# 批量检测
curl -X POST http://localhost:5000/api/v1/batch-detect \
  -H "Content-Type: application/json" \
  -d '{
    "data": [
      {"value": 250.5, "timestamp": "2024-01-15T14:30:00Z"},
      {"value": 180.2, "timestamp": "2024-01-15T14:31:00Z"}
    ]
  }'
```

#### 在应用代码中集成

```python
# 在你的应用中
import requests
import time

def monitor_api_performance():
    api_url = "http://anomaly-detector:5000/api/v1/detect"

    while True:
        # 测量API响应时间
        start = time.time()
        response = requests.get("http://your-api.com/some-endpoint")
        response_time = (time.time() - start) * 1000

        # 检测异常
        detection_result = requests.post(api_url, json={
            "value": response_time
        }).json()

        # 处理异常
        if detection_result['is_anomaly']:
            send_alert(f"API响应时间异常: {response_time}ms, 分数: {detection_result['anomaly_score']}")

        time.sleep(60)  # 每分钟检测一次
```

这份超详细的文档涵盖了从函数命名含义到生产环境部署的所有细节。每个步骤都有详细的解释和实际可运行的代码示例。有任何不清楚的地方，随时问我！
