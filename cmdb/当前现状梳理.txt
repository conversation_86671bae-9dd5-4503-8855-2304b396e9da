运维管理平台

可以做什么




现状	可建设或优化	备注
运维平台	● DB相关流程优化或工具
● 服务器申请流程建设
● nginx统一入口？（ap/knginx/自建）
门户：已有techlink？

	 
可视化	● 成本：基于RMS（建设中）
● 全链路探活（建设中）
● SLA（建设中）
● 发版分析与运营：成功率、构建时长、次数等等？

	

日志	● nginx：knginx的日志可能拿不到？
● 埋点：网络类分析与AI告警？

	

平台	● CMDB（EDC场景 服务器信息缺失）
● EDC与中台合并（天琴、pcs、容器等）【长期任务】
● iptables管理平台（EDC场景，优先级低）	

基建	● 快手：基于Kstrace报文异动检测？
● 行业：基于 BPF 的可观测能力？	


 
 
 

 


mxy
【构建以业务为核心的运维中台】
关于综合运维平台：如何打造出有价值的，可持续的东西
● 从中台的角度：感觉不可行，不管是从基建，云服务，中间件层面；其实已经存在标准化的建设和设施以及专业的运维团队（专攻中台，不对接具体的业务）
去造一个他们的轮子本身意义不大，中台存在哪些限制？
● 不够灵活，不对接具体业务，因此确保业务方向上的匹配度，业务直接对接会存在一定的门槛，比如窗口期限制，kngx不支持丰富的rewrite规则，中台一般都具备一定的通用性，但是在具体一些更加复杂的，明细的场景反而缺少灵活性【复杂度和易用性要有取舍】（是否存在一些机会？以sre的视角将业务和中台之间的一些壁垒打破）；
SRE对应的服务主要对内么？
一些服务，如kim，docs，主要对内使用；这样，就无法避开办公网；是否可以考虑和办公网的一些内容做集成；【少不了办公网的一些服务监控，是否可以与现有的一些做整合？】
发力点是什么
面向效率+企业应用部门的场景化运维能力，必须深入了解业务；而不只是浮于表面；
和基础技术部相比，差异化的能力是什么
● 业务的场景：针对企业应用和效率工程的“具体的业务需求“，在通用性之外覆盖不到的地方；
● 最后一公里：业务 -> sre <- 中台（这中间能做点啥？）

既然是赋能业务，降低业务复杂度，那么可以做到是什么【不要去替代，而是做助推剂，有点类似中介对接买家和卖家】
● 如nginx配置平台，支持复杂的rewrite，补齐kngx的短板（占当前工作百分比是多少呢，如果是大头，那么平台化，收益是可观的），配置变更审计和回滚；
● 目前的成本优化是怎么做的？比如闲置资源是如何确认的，人工么？如何对接finops的，业务如何知道自己手里的机器到底是个什么使用情况，机器的使用是不是合理的。比如有一台64c128g的服务器，可能月均负载连1%都不到，业务清楚么？业务对自己使用了多少资源，花了多少钱有没有概念；
● 监控方面：
	○ 结合AI的可能，目前的全链路拨测，在此基础上基于探活结果除了告警之外还有没有其他的用途，能不能给AI用来做训练和分析，预测
	○ 编写一些MCP对接内部的系统？
	○ 结合AI的服务器配置参数分析？通过Agent将数据采集并上报；
	○ 举例，比如说，突然发现办公网nxdomain飞速上升，分析问题；
	○ 及时让办公网络发现一些问题，比如出口丢包？线路切换？
	○ 日志的可行性；目前我们能接入日志的内容是有限的；
	○ 告警的智能降噪；告警可否从多个不同源头中去粗取精？【根因分析，告警的聚合，告警优先级，结合业务分类，业务的评级，初步可以使用AI提示词+rag的方式去处理，就算是日志的话，也不一定要全部都接入，可以先介入几个业务做一个尝试，拿那些我们可以拿到的日志做一个尝试；先不用搞很复杂的算法，就是借助一些metric+日志配合提示词，知识库，做一些故障分析和预测】
● 综效管理平台：
护城河是啥？【最好还是和业务绑定，不仅仅是做一个执行者；可以使用我们的技术为业务带来价值上的转化】
我们服务的对象是企业应用，内部应用，我们是夹在IT和IDC之间；
我们会同时面临办公网+生产网混合的场景；【建立在对业务有详细的了解】基于这个场景，我们可以构造我们一个故障，运维模式的库；这个是其他的场景和环境不具备的。
我们的链路包含了办公网到idc的完整链路；
作为效率工程或者企业应用的运维中台？连接业务和实际的公司中台？（我自己的体验，我对接效率的BPM，问题非常多，文档不规范，有一些问题也不清晰透明，需要不断的去和BPM引擎的研发去沟通；）
可能的价值：业务的需求 to 技术方案或者方法论；技术指标和业务价值挂钩
运维标准由我们来制定；
知识库的建设？？
目前具备的
彼此的cmdb是否可以合并？
基于AI+CMDB
● SLA标准的指定（已具备）
行业内在干啥？


