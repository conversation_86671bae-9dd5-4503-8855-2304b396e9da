离职工单kafka消息
- userName 邮箱前缀
- userDisplayName 姓名
- dimissionApplyId 离职申请编号
- statusCode int 状态码
		- 1：进行中（需要关注）
		- 2：已离职
		- 3：已终止
		- 4：未开始
		- 5：撤销离职（需要关注）
- applyDate：离职申请日期
- dimissionLastDay：离职日期
- permissionHandoverUserName：权限交接人姓名（这个按业务可能有不同，仅作参考）
- procedures：当前待审批的手续（可能为空）
	- procedureId：环节的UUID
	- procedureName：环节的名称
	- dealUsers：代办人列表 是一个User对象的array
- User：用户对象
	- userId：工号
	- userName：办理人用户名
	- userDisplayName：办理人的姓名

消费离职信息需要在青藤的ConsumerGroup申请对应的topic

## 离职系统回调

当下游系统审批完成以后要通知离职系统，否则会在这个节点卡住；推送消息的方式就是给k对应的kafka topic回传一条消息；对应的object格式如下：

- userName：邮箱前缀
- status：1，固定位1，你通过你就发，不通过你就别发；
- dimissionApplyId：离职申请单的编号，也就是工单ID是什么；
- procedureId：当前你这手续的所处环节的UUID是什么，因为可能存在多个审批阶段；
- approveDate：办理日期，是一个string；

## 关于数据

不管是从kafka拉到的数据，还是回调kafka的数据，都需要进行加解密；