各位好，我做一下自我介绍，我叫马晓雨，主要负责SRE支持相关的工作；今天本来还有个同事，张星，和我一起过来，但是今天他临时有事，所以就我这边自己过来参加这个会议；

之前和锐哥这边沟通，我们希望能够成为业务的BP，更多的了解业务并帮助业务解决问题，因此后续会持续参与这个周例会，进行旁听，并给大家带来一些信息上的同步；

各位后续如果有任何问题，都可以联系我和张星，同时我们也希望更多的接触业务，希望得到大家的一些信息，包括问题之类的，并站在SRE的角度，去帮助业务解决一些问题，给到相关的支持；如果一些不是那么清晰的问题也可以找到我们，我们来帮助做问题的分析以及相关的协调工作；

---

周报，每个小组事情和进展；过评论；告警治理，慢sql，慢接口；系统管的是端到端；
历史债务；现在在治理慢接口慢查询；提供报表，每周看到进展和问题；企业应用内部系统，稳定性和性能上
希望每周持续看到变化，有目标，有推进，sre来进行推进；

为什么不知道故障，没有报出来；需要让锐哥了解这个事情；事情会很被动；问题要处理，要根治；

优先看top5或者top10的慢查询；

1. 高敏数据接入，数据分发链路统一 & 配置化；

2. 统一权限平台；权限平台能力建设；很多自测，应该是QA来测试；kaleido自测和排期；

3. 万相人才接入试点。

企业应用的OKR会补充一些北极星的指标；也是OKR；

OpenAPI的去中心化升级；OpenAPI接口请求很慢，抖动；偶发耗时会很长；
在治理oom，有一波oom，目前还在陆续进行治理；

AI相关开发比较多，最好别用外面的东西了；用自己公司的东西会方便一些；
公司内部的AI网关能力建设；万擎的资源还是会很丰富的，会比私有化部署划算很多；
Prompt规范，协作规范，mcp，rag等；用一下，能踩坑还是踩坑一些；

自己开发的东西自己不用，8%；

权限管理端建设：基于表达式的权限管理；Kaleido
使用成本比较高；

开发前端时间长，8月1日到8月22日，两个人，20天，共40个pd；

竞对薪酬项目：如果数据不准确，这个项目要及时止损；包括从脉脉上收集来的数据，也是不是很准确；
通过爬虫，小红书，脉脉去爬取对应的信息，

---

锐哥当前提的是希望能够输出一些关于告警，以及业务慢sql和慢接口的一些信息；慢sql和慢接口的问题，我们还在整理中，后续例会我们会逐步补齐；

今天我这边带来的主要是关于告警接手率这一个信息；
6月份的时候，企业应用-服务研发中心 HR服务端组当时的告警接手率是78.33%，然后我们昨天查了一下，发现已经达到92.19%，已经达标了；数据为最近7天，不含0点到早晨7点的数据；

按照我们目前约定的标准是大于等于90%就算是达标的；

后面我们打算从告警和稳定性的角度来看一下可以帮大家去做哪些事情

今天我要同步的内容大概就这些，谢谢各位；

出一个代表，后续同步信息：王建飞，周报，想要的告警信息治理数据，慢接口，慢查询的数据，从下周开始

T2的405；

治理，指标大数，哪个组，top5，标在人头上；

我觉得可以线看一部分；

1w3千步；

# 先不用介绍和提的；

当前接手率比较高了，但是消息数还是挺多的，这个我们需要进一步了解都是什么告警，我们才能进一步判断这个高是否是合理的；当前先不做过多的分析了；
不介绍公司的接手率；


# 补充一些行业内的

行业内针对告警接手率其实没有明确的一个要求，因为业务，领域不同会导致告警数量包括关注度，告警维度，内容都可能不太一样。

google sre实践中要求最理想的情况下，应该是应接手，尽可能的都要接受，当前行业内有一些比较泛的标准吧。

- 高优的告警：85-95%接手率
- 中等告警：70-85%接手率
- 低优先级告警：50-70%接手率

Netflix，Uber比起接手率会更关注告警的精确度，追求的是更低的误报率；

# 低接手率反应了什么问题？

- 告警质量高低，告警噪音是否过高？
- 团队是否疲于处理大量的告警；
- 是否需要告警优化

那么后续有什么处理措施呢？

后面我们会更多的去了解我们的业务更深入的内容，然后配合大家一起去优化告警。大概从几个方面吧。

- 告警精准度：真正需要人工干预的告警 / 总告警数
- 平均处理时间（MTTR）
- 告警疲劳度 （同类告警在短时间内的重复次数）
- 业务影响关联度（告警与实际业务相关性）

我们也会和大家定期去review一下这个告警规则是否合理，以及是否需要调整；

Todo：
1. 带着问题触发，可以给出一些方案；
2. 慢SQL，慢接口

# 关于月度例会

避免例行化，尽可能的给大家提供更多的有价值的一些事情；
老板更关注有什么优化，之前xx，之后yy；这是需要优化的点；









---

# 实际周会内容整理

企业应用周会形式：
每周五的下午4点～5点；周报内容锐哥会提前查看并标注评论，会上主要针对周报评论内容进行QA，并明确项目之间的合作以及必要的沟通，确认横向的一些事情；
锐哥对SRE的看法：
不是旁听，而是真的会参与到研发后续的工作过程中
锐哥关心的事情
● 告警的治理
● 慢sql（举例，慢sql能慢到50多秒，不能接受；）
● 慢接口
在锐哥的观念里，他们管的是端到端的服务，因此不能说因为kdb或者什么链路等造成整体服务的一个可用性的下降；
因此上述问题在锐哥视角里是一个很重要，需要治理的事情；
之前没有大举去做这些事情，是欠了一些历史债务，以要命的兜底的事情为主；
当前一些平台，综合这边的系统已经具备治理的条件了；
由SRE提供报表（之前找过涛哥），每周提供一些问题，通过报表，看到企业应用内服务的稳定性和性能上，希望看到趋于向好的状态；
希望持续看到变化，最好是可以持续有目标，持续有推进，sre帮助“抽鞭子“，提前和各位研发打好了预防针，避免研发侧反感；
举例，今年感觉故障多了好多，猜测可能有故障瞒报的情况；
故障群中怕研发侧有心理负担，已经将王诩移除群聊。。。
和锐哥沟通，主要先治理排在头部的一些问题，top5就好；

后续对接企业应用接口人：wangjianfei，周报，告警治理，慢接口，慢查询的信息，负责对接后转到业务的群里；
下周开会时间不变，会议室变更为T2 405；

锐哥的希望，从下周开始
● 治理，具体问题属于哪个组，top5，尽可能标记在人头上；
● 指标的一个大数到达一个什么程度；

TODO：
下周例会交付目标
● 准备慢SQL以及慢接口相关数据
● 准备告警治理相关的数据



- GR外部进咱们机房，交钱；先做同步；
- 三方业务周知，提前告知风险，推进整改，不整改的接受风险；做好评估，是否要上到EDC机房；
- VPN的事情继续支持；